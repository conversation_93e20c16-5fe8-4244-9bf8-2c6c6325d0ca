import { useState, type Dispatch, type SetStateAction } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import type { SuggestSubmitToVendor } from '@shared/types/submitVendor';
import { QueryKeys } from '@shared/utils/constants';
import CheckBox from '@shared/uikit/CheckBox';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import { getJobsForLinking, updateBulkCandidate } from '@shared/utils/api/jobs';
import { useMutation } from '@tanstack/react-query';
import useResponseToast from '@shared/hooks/useResponseToast';
import ItemComponent from '@shared/components/Organism/AsyncPickerModal/components/ItemComponent';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';
import CandidateBulkActionSkeleton from './CandidateBulkActionSkeleton';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type Args = {
  checkedIds: string[];
  setCheckedIds: Dispatch<SetStateAction<string[]>>;
};

export function useCandidateBulkActionsStepTwo(): SingleDataItem[] {
  const { t } = useTranslation();
  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const { handleSuccess } = useResponseToast();
  const [totalCount, setTotalCount] = useState('');

  const { data: candidateBulkAction } = useMultiStepFormState(
    'candidateBulkAction'
  );

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('link_jobs'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const { mutate: linkJobMutate, isLoading: isLinkJobLoading } = useMutation({
    mutationFn: updateBulkCandidate,
  });

  const onSubmit = () => {
    linkJobMutate(
      {
        candidateIds: candidateBulkAction?.candidateIds as any[],
        jobIds: checkedIds as any[],
      },
      {
        onSuccess: () => {
          handleSuccess({
            message: t('candidate_link_message'),
            title: t('candidate_link_title'),
          });
        },
      }
    );
  };

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <Flex flexDir="column" className="gap-12">
      <Typography
        color="secondaryDisabledText"
        size={12}
        fontWeight={400}
        className="text-start"
      >
        {checkedIds?.length} {t('of')} {totalCount} {t('jobs_selected')}
      </Typography>
      <TwoButtonFooter
        submitLabel={t('link')}
        secondaryButtonLabel={t('discard')}
        disabledSubmit={!checkedIds?.length || isLinkJobLoading}
        onSubmitClick={onSubmit}
        secondaryButtonOnClick={() => setStep(0)}
      />
    </Flex>
  );

  const handleCheckBox = (id: string, isSelected: boolean) => {
    if (isSelected) {
      if (checkedIds?.length >= +totalCount) return;
      setCheckedIds((prev) => [...prev, id]);
    } else {
      setCheckedIds((prev) => prev?.filter((val) => val !== id));
    }
  };

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,

      renderBody: () => (
        <SearchableAsyncList<SuggestSubmitToVendor>
          variant="multi"
          name={QueryKeys.getSuggestCompany}
          renderItem={({ item }) => (
            <ItemComponent
              key={item?.id}
              image={item?.croppedImageUrl}
              title={item.title}
              subTitle={item?.username}
            >
              <CheckBox
                value={checkedIds.find((val) => item?.id === val)}
                onChange={(isSelected: boolean) =>
                  handleCheckBox(item?.id, isSelected)
                }
              />
            </ItemComponent>
          )}
          pageSize={10}
          setTotalCount={setTotalCount}
          params={{
            status: 'OPEN',
          }}
          normalizer={(values) => values?.content?.map((item: any) => item)}
          keywords="text"
          apiFunc={getJobsForLinking}
          placeholder={t('search_submit_vendor')}
          renderLoading={<CandidateBulkActionSkeleton />}
          renderNextPageLoading={<CandidateBulkActionSkeleton />}
          enableInfiniteScroll
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
