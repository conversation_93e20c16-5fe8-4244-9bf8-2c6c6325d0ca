import React from 'react';
import useBackToModal from '@shared/hooks/useBackToModal';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import IconButton from '@shared/uikit/Button/IconButton';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import { suggestSubmittedJob } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import eventKeys from 'shared/constants/event-keys';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import ItemComponent from '../../AsyncPickerModal/components/ItemComponent';
import type { MultiStepFormProps } from '../MultiStepForm';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import type { SubmittedUnSubmitted } from '@shared/types/job';

type SingleDataItem = MultiStepFormStepProps & {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
};

export function useSubmitCandidateStepOne(): SingleDataItem[] {
  const { t } = useTranslation();
  const { hasBackModal, backToModal } = useBackToModal('createEntityPanel');

  const { data: submitCandidate } = useMultiStepFormState('submitCandidate');
  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('submit_candidate'),
    hideBack: !hasBackModal,
    noCloseButton: hasBackModal,
    backButtonProps: {
      onClick: () => {
        backToModal();
        closeMultiStepForm('submitCandidate');
        event.trigger(eventKeys.closeModal);
      },
    },
  });

  const getStepHeaderProps: SingleDataItem['getStepHeaderProps'] = () => ({
    title: t('choose_job'),
    iconProps: {
      name: 'briefcase',
      type: 'fal',
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = () => null;

  const data: Array<SingleDataItem> = [
    {
      stepKey: '1',
      getHeaderProps,
      getStepHeaderProps,
      renderBody: ({ setStep, setFieldValue }) => (
        <SearchableAsyncList<SubmittedUnSubmitted>
          variant="single"
          name={QueryKeys.getSuggestSubmittedUnSubmittedJobs}
          listItemsClassName="mt-12"
          renderItem={({ item }) => (
            <ItemComponent
              key={item?.id}
              image={item?.imageUrl}
              title={item?.title}
              subTitle={item?.locationName}
              onClick={() => {
                setStep(1);
                setFieldValue('job', item);
              }}
            >
              <IconButton
                name="chevron-right"
                colorSchema="transparent"
                size="md20"
                variant="rectangle"
                noHover
              />
            </ItemComponent>
          )}
          params={{
            clientId: submitCandidate?.vendorId,
          }}
          keywords="text"
          apiFunc={suggestSubmittedJob}
          placeholder={t('search_job')}
          normalizer={(values) => values?.content?.map((item: any) => item)}
          enableInfiniteScroll
          pageSize={10}
        />
      ),
      renderFooter,
    },
  ];

  return data;
}
