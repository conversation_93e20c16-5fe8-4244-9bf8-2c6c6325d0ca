import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import eventKeys from 'shared/constants/event-keys';
import useBackToModal from '@shared/hooks/useBackToModal';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import { QueryKeys } from '@shared/utils/constants';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import IconButton from '@shared/uikit/Button/IconButton';
import type { SubmittedUnSubmitted } from '@shared/types/job';
import { getSubmittedUnsubmitted } from '@shared/utils/api/project';
import type { MultiStepFormProps } from '../MultiStepForm';
import SubmitVendorItemSkeleton from './SubmitJobItemSkeleton';
import ItemComponent from '../../AsyncPickerModal/components/ItemComponent';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
};

type SubmitJobStepOneProps = {
  setJobId: React.Dispatch<React.SetStateAction<number | undefined>>;
  setIsSubmitted: React.Dispatch<React.SetStateAction<boolean>>;
};

export function useSubmitJobStepOne({
  setJobId,
  setIsSubmitted,
}: SubmitJobStepOneProps): SingleDataItem[] {
  const { t } = useTranslation();
  const { hasBackModal, backToModal } = useBackToModal('createEntityPanel');

  const { data: submitJob } = useMultiStepFormState('submitJob');

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('submit_job'),
    hideBack: !hasBackModal,
    noCloseButton: hasBackModal,
    backButtonProps: {
      onClick: () => {
        backToModal();
        closeMultiStepForm('submitJob');
        event.trigger(eventKeys.closeModal);
      },
    },
  });

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,

      renderBody: ({ setStep }) => (
        <SearchableAsyncList<SubmittedUnSubmitted>
          variant="single"
          name={QueryKeys.getSuggestSubmittedUnSubmittedJobs}
          listItemsClassName="mt-12"
          renderItem={({ item }) => (
            <ItemComponent
              key={item?.id}
              image={item?.imageUrl}
              title={item?.title}
              subTitle={item?.locationName}
              onClick={() => {
                setStep(1);
                setJobId(item?.id);
                setIsSubmitted(item?.submitted);
              }}
            >
              <Flex flexDir="row" className="gap-8 items-center">
                {!!item?.submitted && (
                  <Typography color="success" fontSize={15} fontWeight={700}>
                    {t('submitted')}
                  </Typography>
                )}
                <IconButton
                  name="chevron-right"
                  colorSchema="primary"
                  size="md20"
                  variant="rectangle"
                />
              </Flex>
            </ItemComponent>
          )}
          params={{
            vendorId: submitJob?.vendorId
              ? Number(submitJob?.vendorId)
              : undefined,
          }}
          normalizer={(values) => values?.content?.map((item: any) => item)}
          keywords="text"
          enableInfiniteScroll
          pageSize={10}
          apiFunc={getSubmittedUnsubmitted}
          placeholder={t('search_job')}
          renderLoading={<SubmitVendorItemSkeleton />}
        />
      ),
    },
  ];

  return data;
}
