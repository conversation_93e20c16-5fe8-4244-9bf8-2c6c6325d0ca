@import '/src/shared/theme/theme.scss';

@layer layout {
  .appRoot {
    height: 100%;
    background-color: colors(background);
  }

  .appMain {
    flex-direction: row;
    height: 100%;
    position: relative;
    margin-top: variables(headerMobileHeight);
    overflow: auto;
    overflow-x: hidden;
  }

  .mainLeft {
    position: sticky;
    top: 0;
    overflow: auto;
    flex-shrink: 0;
  }

  .mainCenter {
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -moz-box-flex: 1;
    -ms-flex: 1;
    flex-grow: 1;
    background-color: colors(background2);
    height: 100%;
    overflow-x: hidden;
  }

  .mainRight {
    position: fixed;
    top: variables(mainHeaderDesktopHeight);
    bottom: 0;
    right: 0;
    z-index: 999;
  }

  .dark {
    pointer-events: none;
  }

  .disableDebounce {
    overflow: hidden;
  }

  .leftDrawerContent {
    top: variables(mainHeaderDesktopHeight);
  }

  @media (min-width: breakpoints(tablet)) {
    .appMain {
      margin-top: variables(mainHeaderDesktopHeight);
    }
    .mainCenter {
      margin-right: unset;
    }
    .mainCenterLoggedIn {
      margin-right: variables(sidePanelWidth);
    }
    .noMarginTop {
      margin-top: 0 !important;
    }
  }
}
