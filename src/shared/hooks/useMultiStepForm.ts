import { useStore } from '@tanstack/react-store';
import { Store } from '@tanstack/store';
import { type CandidateFormStepKeys } from '@shared/components/Organism/MultiStepForm/CreateCandidateForm/constants';
import isClient from 'shared/utils/toolkit/isClient';
import type { CheckoutModalProps } from '@shared/components/Organism/MultiStepForm/CheckoutForm';
import type { DeleteEntityModalType } from '@shared/components/Organism/MultiStepForm/DeleteEntityModal';
import type { CandidateFormData } from '@shared/types/candidates';
import type { CreateJobAPIDataProps } from '@shared/types/jobsProps';
import type { QuestionAPIProps } from '@shared/types/questionsProps';
import type { IconName } from '@shared/uikit/Icon/types';

interface DeleteEntityModalProps {
  isOpen: boolean;
  data: any;
  variant?: DeleteEntityModalType;
}

export type MultiStepModalProps<PropTypes> =
  | { isOpen: false }
  | ({ isOpen: true } & PropTypes);

export interface CreateCandidateModalProps {
  isOpen: boolean;
  stepKey?: keyof typeof CandidateFormStepKeys;
  data?: CandidateFormData;
  options?: {
    onCreate?: (data: CandidateFormData) => void;
    onUpdate?: (data: CandidateFormData) => void;
    onFailure?: () => void;
  };
}

const initialValue = {
  profileAboutEdit: {
    isOpen: false as boolean,
    openningPathname: '' as string,
    isSingleStep: false as boolean,
    stepKey: '' as string | undefined,
    focusedField: '' as string | undefined,
    data: null as Record<any, any> | null | undefined,
  },
  editProfileSections: {
    isOpen: false as boolean,
    openningPathname: '' as string,
    isSingleStep: false as boolean,
    stepKey: '' as string | undefined,
    focusedField: '' as string | undefined,
    data: null as Record<any, any> | null | undefined,
    highlightModal: {
      isOpen: false as boolean,
      data: null as any,
    },
  },
  resumeUpload: {
    isOpen: false as boolean,
    openningPathname: '' as string,
    data: null as Record<any, any> | null | undefined,
    tempInitialData: null as any,
  },
  createPageForm: {
    isOpen: false as boolean,
    openningPathname: '' as string,
    isSingleStep: false as boolean,
    stepKey: '' as string | undefined,
    focusedField: '' as string | undefined,
    data: null as Record<any, any> | null | undefined,
  },
  invitePeople: {
    isOpen: false as boolean,
    openningPathname: '' as string,
    data: {
      initialMethod: undefined as any,
      entity: undefined,
    },
  },
  submitToVendor: {
    isOpen: false as boolean,
    openningPathname: '' as string,
    data: {
      step: '1' as string,
      jobId: '' as string,
    },
  },
  submitJob: {
    isOpen: false,
    data: {
      step: '1' as string,
      vendorId: '' as string,
    },
  },
  submitCandidate: {
    isOpen: false as boolean,
    data: {
      step: '1' as string,
      vendorId: '' as string,
    },
  },
  candidateBulkAction: {
    isOpen: false as boolean,
    data: {
      step: '1' as string,
      candidateIds: [] as string[],
    },
  },
  pipelineBulkAction: {
    isOpen: false as boolean,
    data: {
      step: '1' as string,
      participationIds: [] as string[],
    },
  },
  jobApplication: {
    isOpen: false as boolean,
    openningPathname: '' as string,
    focusedField: '' as string | undefined,
    stepKey: '' as string | undefined,
    data: undefined as
      | (Omit<CreateJobAPIDataProps, 'questions'> & {
          id: string;
          questions: QuestionAPIProps[];
        })
      | undefined,
  },
  createProjectForm: {
    isOpen: false as boolean,
  },
  createJobForm: {
    isOpen: false as boolean,
    data: undefined as Record<any, any> | undefined,
    options: {
      subForm: undefined as string | undefined,
      step: undefined as number | undefined,
    },
  },
  createCandidateForm: {
    isOpen: false,
    data: undefined,
  } as CreateCandidateModalProps,
  linkJobForm: {
    isOpen: false as boolean,
    data: undefined as
      | {
          id: string;
          target: 'project' | 'candidate' | 'job';
          initialJobs: Array<{ id: string }>;
          cardProps?: {
            title: string;
            text: string;
            icon?: IconName;
            image?: string;
          };
        }
      | undefined,
  } as any,
  deleteEntityModal: {
    isOpen: false,
    data: undefined,
    variant: undefined,
  } as DeleteEntityModalProps,
  checkout: {
    isOpen: false,
  } as CheckoutModalProps,
  submitToClientForm: {
    isOpen: false,
    data: {
      clientId: undefined,
      candidateId: undefined,
      jobIds: [],
      jobOfCandidateIds: [],
    },
    stepKey: 'client' as 'client' | 'job',
  } as {
    isOpen: boolean;
    data: {
      clientId?: number;
      candidateId?: number;
      jobIds: number[];
      jobOfCandidateIds: number[];
    };
    stepKey: 'client' | 'job';
  },
  addCompany: {
    isOpen: false,
  },
  submitJobModal: {
    isOpen: false,
  },
  createTodoForm: {
    isOpen: false,
    data: {
      target: 'list' as 'list' | 'form',
    },
  } as {
    isOpen: boolean;
    data: {
      target: 'list' | 'form';
    };
  },
  createNoteForm: {
    isOpen: false,
    data: {
      target: 'list' as 'list' | 'form',
    },
  },
} as const;

export const multiStepFormStore = new Store(initialValue);

export type FormNames = keyof typeof initialValue;

type InitialValuesType = typeof initialValue;
export interface MultiStepFormState extends InitialValuesType {
  checkout: CheckoutModalProps;
}
export function useMultiStepFormState<T extends FormNames>(formName: T) {
  const state = useStore(multiStepFormStore, (prev) => prev[formName]);
  if (!state) return {} as (typeof initialValue)[T];

  return state as (typeof initialValue)[T];
}

export const openMultiStepForm = ({
  stepKey,
  focusedField,
  data,
  formName,
  keepPreviousData,
  variant,
  options,
}: {
  stepKey?: string;
  focusedField?: string;
  data?: any;
  formName: FormNames;
  keepPreviousData?: boolean;
  variant?: DeleteEntityModalType;
  options?: Record<string, any>;
}) => {
  multiStepFormStore.setState((state) => ({
    ...state,
    [formName]: {
      ...state[formName],
      // We will compare this pathname with the current pathname, whenver it's changed, it's time to close the modal
      openningPathname: isClient() ? window.location.pathname : '',
      isOpen: true,
      isSingleStep: !!stepKey,
      stepKey,
      focusedField,
      variant,
      options,
      ...(keepPreviousData ? {} : { data }),
    },
  }));
};

export const closeMultiStepForm = (formName: FormNames, data?: any) => {
  multiStepFormStore.setState((state) => ({
    ...state,
    [formName]: {
      ...initialValue[formName],
      isOpen: false,
      data: data || null,
    },
  }));
};

export const closeAllMultiStepForms = () => {
  const { state } = multiStepFormStore;

  const copiedState = Object.entries(state).reduce(
    (acc, [key, value]) => ({ ...acc, [key]: { ...value, isOpen: false } }),
    state
  );

  multiStepFormStore.setState(() => copiedState);
};

export function setMultiStepFormData<FormName extends FormNames>(
  formName: FormName,
  payload: Partial<MultiStepFormState[FormName]>
) {
  multiStepFormStore.setState((state) => ({
    ...state,
    [formName]: {
      ...state[formName],
      ...payload,
    },
  }));
}
